import 'dart:io';
import 'package:dio/dio.dart';
import 'package:get/get.dart' hide Response, FormData, MultipartFile;

class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final int? statusCode;
  final Map<String, dynamic>? errors;

  ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.statusCode,
    this.errors,
  });

  factory ApiResponse.success(T data, {String? message, int? statusCode}) {
    return ApiResponse(
      success: true,
      data: data,
      message: message,
      statusCode: statusCode ?? 200,
    );
  }

  factory ApiResponse.error(
    String message, {
    int? statusCode,
    Map<String, dynamic>? errors,
  }) {
    return ApiResponse(
      success: false,
      message: message,
      statusCode: statusCode ?? 500,
      errors: errors,
    );
  }
}

class ApiService extends GetxService {
  late Dio _dio;
  final RxString _authToken = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeDio();
  }

  void _initializeDio() {
    _dio = Dio(
      BaseOptions(
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        sendTimeout: const Duration(seconds: 30),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    );

    // Add request interceptor for authentication
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          if (_authToken.value.isNotEmpty) {
            options.headers['Authorization'] = 'Bearer ${_authToken.value}';
          }
          handler.next(options);
        },
        onResponse: (response, handler) {
          handler.next(response);
        },
        onError: (error, handler) {
          if (error.response?.statusCode == 401) {
            // Handle unauthorized access
            _handleUnauthorized();
          }
          handler.next(error);
        },
      ),
    );
  }

  void setAuthToken(String token) {
    _authToken.value = token;
  }

  void clearAuthToken() {
    _authToken.value = '';
  }

  void _handleUnauthorized() {
    // Clear token and redirect to login
    clearAuthToken();
    // You can add navigation logic here if needed
    Get.snackbar(
      'خطأ في المصادقة',
      'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى',
    );
  }

  Future<ApiResponse<T>> _handleResponse<T>(
    Future<Response> request,
    T Function(dynamic) fromJson,
  ) async {
    try {
      final response = await request;

      // Dio considers 2xx status codes as successful
      final data = response.data;

      if (data is Map<String, dynamic>) {
        if (data['success'] == true || response.statusCode == 200) {
          return ApiResponse.success(
            fromJson(data['data'] ?? data),
            message: data['message'],
            statusCode: response.statusCode,
          );
        } else {
          return ApiResponse.error(
            data['message'] ?? 'حدث خطأ غير متوقع',
            statusCode: response.statusCode,
            errors: data['errors'],
          );
        }
      }

      return ApiResponse.success(
        fromJson(data),
        statusCode: response.statusCode,
      );
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.receiveTimeout ||
          e.type == DioExceptionType.sendTimeout) {
        return ApiResponse.error('انتهت مهلة الاتصال');
      } else if (e.type == DioExceptionType.connectionError) {
        return ApiResponse.error('خطأ في الاتصال بالشبكة');
      } else if (e.response != null) {
        final responseData = e.response!.data;
        if (responseData is Map<String, dynamic>) {
          return ApiResponse.error(
            responseData['message'] ?? 'حدث خطأ غير متوقع',
            statusCode: e.response!.statusCode,
            errors: responseData['errors'],
          );
        }
        return ApiResponse.error(
          'حدث خطأ غير متوقع',
          statusCode: e.response!.statusCode,
        );
      }
      return ApiResponse.error('حدث خطأ غير متوقع: ${e.message}');
    } on SocketException {
      return ApiResponse.error('خطأ في الاتصال بالشبكة');
    } on FormatException {
      return ApiResponse.error('خطأ في تنسيق البيانات');
    } catch (e) {
      return ApiResponse.error('حدث خطأ غير متوقع: ${e.toString()}');
    }
  }

  // GET request
  Future<ApiResponse<T>> get<T>(
    String endpoint,
    T Function(dynamic) fromJson, {
    Map<String, dynamic>? query,
  }) async {
    return _handleResponse(
      _dio.get(endpoint, queryParameters: query),
      fromJson,
    );
  }

  // POST request
  Future<ApiResponse<T>> post<T>(
    String endpoint,
    T Function(dynamic) fromJson, {
    dynamic body,
    Map<String, dynamic>? query,
  }) async {
    return _handleResponse(
      _dio.post(endpoint, data: body, queryParameters: query),
      fromJson,
    );
  }

  // PUT request
  Future<ApiResponse<T>> put<T>(
    String endpoint,
    T Function(dynamic) fromJson, {
    dynamic body,
    Map<String, dynamic>? query,
  }) async {
    return _handleResponse(
      _dio.put(endpoint, data: body, queryParameters: query),
      fromJson,
    );
  }

  // DELETE request
  Future<ApiResponse<T>> delete<T>(
    String endpoint,
    T Function(dynamic) fromJson, {
    Map<String, dynamic>? query,
  }) async {
    return _handleResponse(
      _dio.delete(endpoint, queryParameters: query),
      fromJson,
    );
  }

  // PATCH request
  Future<ApiResponse<T>> patch<T>(
    String endpoint,
    T Function(dynamic) fromJson, {
    dynamic body,
    Map<String, dynamic>? query,
  }) async {
    return _handleResponse(
      _dio.patch(endpoint, data: body, queryParameters: query),
      fromJson,
    );
  }

  // Upload file
  Future<ApiResponse<T>> uploadFile<T>(
    String endpoint,
    String filePath,
    T Function(dynamic) fromJson, {
    String fieldName = 'file',
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final formData = FormData.fromMap({
        fieldName: await MultipartFile.fromFile(
          filePath,
          filename: filePath.split('/').last,
        ),
        if (additionalData != null) ...additionalData,
      });

      return _handleResponse(_dio.post(endpoint, data: formData), fromJson);
    } catch (e) {
      return ApiResponse.error('فشل في رفع الملف: ${e.toString()}');
    }
  }

  // Download file
  Future<ApiResponse<String>> downloadFile(
    String endpoint,
    String savePath,
  ) async {
    try {
      final response = await _dio.get(
        endpoint,
        options: Options(responseType: ResponseType.bytes),
      );

      final file = File(savePath);
      await file.writeAsBytes(response.data);

      return ApiResponse.success(savePath, message: 'تم تحميل الملف بنجاح');
    } catch (e) {
      if (e is DioException) {
        return ApiResponse.error(
          'فشل في تحميل الملف',
          statusCode: e.response?.statusCode,
        );
      }
      return ApiResponse.error('فشل في تحميل الملف: ${e.toString()}');
    }
  }
}
