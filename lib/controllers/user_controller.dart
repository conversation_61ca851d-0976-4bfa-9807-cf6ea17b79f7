import 'package:get/get.dart';
import 'package:myrunway/core/models/user_model.dart';
import 'package:myrunway/core/constants/user_roles.dart';
import 'package:myrunway/core/services/auth_service.dart';

class UserController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();

  // Reactive variables
  final Rx<UserModel?> _user = Rx<UserModel?>(null);
  final RxBool _isLoading = false.obs;
  final RxString _selectedOfficeId = ''.obs;
  final RxList<UserModel> _teamMembers = <UserModel>[].obs;

  // Getters
  UserModel? get user => _user.value;
  bool get isLoading => _isLoading.value;
  String get selectedOfficeId => _selectedOfficeId.value;
  List<UserModel> get teamMembers => _teamMembers;

  // User role getters
  bool get isEmployee => _user.value?.isEmployee ?? false;
  bool get isManager => _user.value?.isManager ?? false;
  bool get isMaster => _user.value?.isMaster ?? false;
  UserRole? get userRole => _user.value?.role;

  // Permission getters
  bool get canManageEmployees => _user.value?.role.canManageEmployees ?? false;
  bool get canManageOrders => _user.value?.role.canManageOrders ?? false;
  bool get canManageClients => _user.value?.role.canManageClients ?? false;
  bool get canManageOffices => _user.value?.role.canManageOffices ?? false;
  bool get canAccessReports => _user.value?.role.canAccessReports ?? false;
  bool get canAccessAdvancedReports =>
      _user.value?.role.canAccessAdvancedReports ?? false;
  bool get canManageSystemSettings =>
      _user.value?.role.canManageSystemSettings ?? false;

  @override
  void onInit() {
    super.onInit();
    _initializeUser();
  }

  void _initializeUser() {
    // Set initial user
    _user.value = _authService.currentUser;
    if (_user.value != null) {
      _selectedOfficeId.value = _user.value!.officeId ?? '';
      _loadTeamMembers();
    }

    // Listen to auth service changes by checking periodically
    // In a real app, you would use a proper stream or callback mechanism
    ever(_authService.isLoggedIn.obs, (bool isLoggedIn) {
      if (isLoggedIn) {
        _user.value = _authService.currentUser;
        if (_user.value != null) {
          _selectedOfficeId.value = _user.value!.officeId ?? '';
          _loadTeamMembers();
        }
      } else {
        _user.value = null;
        _selectedOfficeId.value = '';
        _teamMembers.clear();
      }
    });
  }

  Future<void> _loadTeamMembers() async {
    if (_user.value == null) return;

    _isLoading.value = true;

    try {
      // In a real app, this would fetch from API
      // For now, we'll use mock data
      _teamMembers.value = _getMockTeamMembers();
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تحميل بيانات الفريق');
    } finally {
      _isLoading.value = false;
    }
  }

  List<UserModel> _getMockTeamMembers() {
    // TODO: Implement real API call to get team members
    // For now, return empty list since we're focusing on authentication
    return [];
  }

  Future<void> updateUser(UserModel updatedUser) async {
    _user.value = updatedUser;
  }

  Future<void> refreshUserData() async {
    if (_user.value == null) return;

    _isLoading.value = true;

    try {
      // In a real app, this would fetch updated user data from API
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      await _loadTeamMembers();
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تحديث البيانات');
    } finally {
      _isLoading.value = false;
    }
  }

  void selectOffice(String officeId) {
    _selectedOfficeId.value = officeId;
    _loadTeamMembers();
  }

  bool hasPermission(UserRole requiredRole) {
    return _authService.hasPermission(requiredRole);
  }

  String get userDisplayName => _user.value?.name ?? 'مستخدم';
  String get userInitials => _user.value?.initials ?? 'U';
  String get userEmail => _user.value?.email ?? '';
  String get userPhone => _user.value?.phone ?? '';
  String get userRoleDisplay => _user.value?.displayRole ?? '';
  String get userOfficeName => _user.value?.officeName ?? '';

  // Statistics getters (these would typically come from API)
  int get totalOrdersAssigned {
    // Mock data - in real app, this would come from API
    switch (_user.value?.role) {
      case UserRole.employee:
        return 15;
      case UserRole.manager:
        return 120;
      case UserRole.master:
        return 500;
      default:
        return 0;
    }
  }

  int get totalOrdersCompleted {
    // Mock data - in real app, this would come from API
    switch (_user.value?.role) {
      case UserRole.employee:
        return 12;
      case UserRole.manager:
        return 95;
      case UserRole.master:
        return 450;
      default:
        return 0;
    }
  }

  double get totalCommissionEarned {
    // Mock data - in real app, this would come from API
    switch (_user.value?.role) {
      case UserRole.employee:
        return 1250.50;
      case UserRole.manager:
        return 8500.75;
      case UserRole.master:
        return 25000.00;
      default:
        return 0.0;
    }
  }

  double get completionRate {
    if (totalOrdersAssigned == 0) return 0.0;
    return (totalOrdersCompleted / totalOrdersAssigned) * 100;
  }
}
